[2025-08-05 15:20:03] localhost.INFO: array (
  'requestdata' => 
  Illuminate\Http\Request::__set_state(array(
     'json' => NULL,
     'convertedFiles' => 
    array (
    ),
     'userResolver' => 
    Closure::__set_state(array(
    )),
     'routeResolver' => 
    Closure::__set_state(array(
    )),
     'attributes' => 
    Symfony\Component\HttpFoundation\ParameterBag::__set_state(array(
       'parameters' => 
      array (
      ),
    )),
     'request' => 
    Symfony\Component\HttpFoundation\InputBag::__set_state(array(
       'parameters' => 
      array (
        'error' => 
        array (
          'code' => 'BAD_REQUEST_ERROR',
          'description' => 'Payment failed',
          'source' => 'gateway',
          'step' => 'payment_authorization',
          'reason' => 'payment_failed',
          'metadata' => '{"payment_id":"pay_R1br9pHfpsBHnH","order_id":"order_R1bqfH4gDCHzwl"}',
        ),
      ),
    )),
     'query' => 
    Symfony\Component\HttpFoundation\InputBag::__set_state(array(
       'parameters' => 
      array (
      ),
    )),
     'server' => 
    Symfony\Component\HttpFoundation\ServerBag::__set_state(array(
       'parameters' => 
      array (
        'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\builds\\wbi\\public',
        'REMOTE_ADDR' => '127.0.0.1',
        'REMOTE_PORT' => '54800',
        'SERVER_SOFTWARE' => 'PHP 8.0.25 Development Server',
        'SERVER_PROTOCOL' => 'HTTP/1.1',
        'SERVER_NAME' => '127.0.0.1',
        'SERVER_PORT' => '8000',
        'REQUEST_URI' => '/secondaryWbiafterpayment',
        'REQUEST_METHOD' => 'POST',
        'SCRIPT_NAME' => '/index.php',
        'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\builds\\wbi\\public\\index.php',
        'PATH_INFO' => '/secondaryWbiafterpayment',
        'PHP_SELF' => '/index.php/secondaryWbiafterpayment',
        'HTTP_HOST' => 'localhost:8000',
        'HTTP_CONNECTION' => 'keep-alive',
        'CONTENT_LENGTH' => '284',
        'HTTP_CONTENT_LENGTH' => '284',
        'HTTP_CACHE_CONTROL' => 'max-age=0',
        'HTTP_SEC_CH_UA' => '"Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"',
        'HTTP_SEC_CH_UA_MOBILE' => '?0',
        'HTTP_SEC_CH_UA_PLATFORM' => '"Windows"',
        'HTTP_ORIGIN' => 'https://api.razorpay.com',
        'CONTENT_TYPE' => 'application/x-www-form-urlencoded',
        'HTTP_CONTENT_TYPE' => 'application/x-www-form-urlencoded',
        'HTTP_UPGRADE_INSECURE_REQUESTS' => '1',
        'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'HTTP_SEC_GPC' => '1',
        'HTTP_ACCEPT_LANGUAGE' => 'en-US,en;q=0.8',
        'HTTP_SEC_FETCH_SITE' => 'cross-site',
        'HTTP_SEC_FETCH_MODE' => 'navigate',
        'HTTP_SEC_FETCH_DEST' => 'document',
        'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd',
        'REQUEST_TIME_FLOAT' => 1754387402.446678,
        'REQUEST_TIME' => 1754387402,
      ),
    )),
     'files' => 
    Symfony\Component\HttpFoundation\FileBag::__set_state(array(
       'parameters' => 
      array (
      ),
    )),
     'cookies' => 
    Symfony\Component\HttpFoundation\InputBag::__set_state(array(
       'parameters' => 
      array (
      ),
    )),
     'headers' => 
    Symfony\Component\HttpFoundation\HeaderBag::__set_state(array(
       'headers' => 
      array (
        'host' => 
        array (
          0 => 'localhost:8000',
        ),
        'connection' => 
        array (
          0 => 'keep-alive',
        ),
        'content-length' => 
        array (
          0 => '284',
        ),
        'cache-control' => 
        array (
          0 => 'max-age=0',
        ),
        'sec-ch-ua' => 
        array (
          0 => '"Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"',
        ),
        'sec-ch-ua-mobile' => 
        array (
          0 => '?0',
        ),
        'sec-ch-ua-platform' => 
        array (
          0 => '"Windows"',
        ),
        'origin' => 
        array (
          0 => 'https://api.razorpay.com',
        ),
        'content-type' => 
        array (
          0 => 'application/x-www-form-urlencoded',
        ),
        'upgrade-insecure-requests' => 
        array (
          0 => '1',
        ),
        'user-agent' => 
        array (
          0 => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ),
        'accept' => 
        array (
          0 => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        ),
        'sec-gpc' => 
        array (
          0 => '1',
        ),
        'accept-language' => 
        array (
          0 => 'en-US,en;q=0.8',
        ),
        'sec-fetch-site' => 
        array (
          0 => 'cross-site',
        ),
        'sec-fetch-mode' => 
        array (
          0 => 'navigate',
        ),
        'sec-fetch-dest' => 
        array (
          0 => 'document',
        ),
        'accept-encoding' => 
        array (
          0 => 'gzip, deflate, br, zstd',
        ),
      ),
       'cacheControl' => 
      array (
        'max-age' => '0',
      ),
    )),
     'content' => NULL,
     'languages' => NULL,
     'charsets' => NULL,
     'encodings' => NULL,
     'acceptableContentTypes' => NULL,
     'pathInfo' => '/secondaryWbiafterpayment',
     'requestUri' => '/secondaryWbiafterpayment',
     'baseUrl' => '',
     'basePath' => NULL,
     'method' => 'POST',
     'format' => NULL,
     'session' => 
    Illuminate\Session\Store::__set_state(array(
       'id' => 'GldGtI52Xvip8s4DxRJqtvmKFjuxghDc2WjYJ9Ml',
       'name' => 'assam_wbi_session',
       'attributes' => 
      array (
        '_token' => 'qCNMjtKyhBf0IeBV6vSNjiMAzHaO9pfSc6LJ4Jex',
      ),
       'handler' => 
      Illuminate\Session\FileSessionHandler::__set_state(array(
         'files' => 
        Illuminate\Filesystem\Filesystem::__set_state(array(
        )),
         'path' => 'C:\\Users\\<USER>\\Desktop\\builds\\wbi\\storage\\framework/sessions',
         'minutes' => '120',
      )),
       'started' => true,
    )),
     'locale' => NULL,
     'defaultLocale' => 'en',
     'preferredFormat' => NULL,
     'isHostValid' => true,
     'isForwardedValid' => true,
     'isSafeContentPreferred' => NULL,
     'isIisRewrite' => false,
  )),
  'razorpay_order_id' => NULL,
  'razorpay_payment_id' => NULL,
  'razorpay_signature' => NULL,
)  

