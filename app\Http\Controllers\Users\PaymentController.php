<?php

namespace App\Http\Controllers\Users;

use App\Helpers\PaymentHelper;
use App\Http\Controllers\Helper\BaseController;
use App\Models\DprPayment;
use App\Models\DprReport;
use App\Models\PaymentTransaction;
use App\Models\SecondaryAgarwoodWbiMaster;
use App\Models\SecondaryAgarwoodWbiPayment;
use App\Models\SecondaryWbiMaster;
use App\Models\SecondaryWbiMasterPayment;
use App\Models\WbiComposite;
use App\Models\WbiCompositePayment;
use App\Models\TransferOfLicenceCompositePayment;
use App\Models\TransferOfLicenceComposite;
use App\Models\WbiMaster;
use App\Models\WbiMasterPayment;
use App\Models\TransferLicencePrimary;
use App\Models\TransferOfLicencePrimaryPayment;
use App\Models\TransferOfLicenceSecondaryNonAgrawood;
use App\Models\TransferOfLicenceSecondayNonAgarwoodPayment;
use App\Models\TransferOfLicenceSecondaryAgrawood;
use App\Models\TransferOfLicenceSecondayAgarwoodPayment;
use Auth, Log;
use Illuminate\Http\Request;

class PaymentController extends BaseController
{
    public function secondaryWbiafterpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            if (isset($requestdata['error'])) {
                $paymenetstatus = "FAILED";
                if (isset($requestdata['error']['metadata'])) {
                    $metadata = $requestdata['error']['metadata'];
                    if (is_string($metadata)) {
                        $decodedMetadata = json_decode($metadata, true);
                        $transactionid = $decodedMetadata['order_id'] ?? null;
                    } else if (is_array($metadata)) {
                        $transactionid = $metadata['order_id'] ?? null;
                    } else {
                        $transactionid = null;
                    }
                } else {
                    $transactionid = null;
                }
            } else {
                if (isset($requestdata['razorpay_order_id']) && isset($requestdata['razorpay_payment_id']) && isset($requestdata['razorpay_signature'])) {
                    $generated_signature = hash_hmac(
                        'sha256',
                        $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                        env('RAZ_HDFC_PAY_SECRET_KEY')
                    );
                    if ($generated_signature == $requestdata['razorpay_signature']) {
                        $paymenetstatus = "SUCCESS";
                    } else {
                        $paymenetstatus = "FAILED";
                    }

                    $transactionid = $requestdata['razorpay_order_id'];
                } else {
                    $paymenetstatus = "FAILED";
                    $transactionid = null;
                }
            }
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $SecondaryWbiMasterPayment = SecondaryWbiMasterPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $SecondaryWbiMasterPayment->transaction_id;
                    $amount = $SecondaryWbiMasterPayment->fee;
                } else {
                    $SecondaryWbiMasterPayment = SecondaryWbiMasterPayment::where('transaction_id', $transactionid)
                        ->first();
                }

                $SecondaryWbiMasterPayment->status = 1; //Success
                $SecondaryWbiMasterPayment->save();

                $secondary_wbi_master = SecondaryWbiMaster::where('id', $SecondaryWbiMasterPayment->secondary_wbi_master_id)->first();
                $secondary_wbi_master->application_status = $SecondaryWbiMasterPayment->payment_type == "APPLICATION" ? 23 : 24;
                $secondary_wbi_master->save();
                // Helper::sendSMS2(1407167713545096968, $secondary_wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $SecondaryWbiMasterPayment->payment_type,
                    $secondary_wbi_master->user_id,
                    $SecondaryWbiMasterPayment->secondary_wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $SecondaryWbiMasterPayment->razorpay_order_id
                );

                Auth::loginUsingId($secondary_wbi_master->user_id);

                if ($SecondaryWbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/secondary-wbi-license/' . $SecondaryWbiMasterPayment->secondary_wbi_master_id . '/edit');
                } else {
                    return redirect('/secondary-wbi-license/update/' . $SecondaryWbiMasterPayment->secondary_wbi_master_id);
                }
            case "FAILED":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    if(!$transactionid){
                        return redirect(env('APP_URL'))->with('error', 'Payment record not found');
                    }
                    
                    $SecondaryWbiMasterPayment = SecondaryWbiMasterPayment::where('razorpay_order_id', $transactionid)->first();
                    $transactionid = $SecondaryWbiMasterPayment->transaction_id;
                    $amount = $SecondaryWbiMasterPayment->fee;
                    
                } else {
                    $SecondaryWbiMasterPayment = SecondaryWbiMasterPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $SecondaryWbiMasterPayment->status = 0; //Failed
                $SecondaryWbiMasterPayment->save();

                $secondary_wbi_master = SecondaryWbiMaster::where('id', $SecondaryWbiMasterPayment->secondary_wbi_master_id)->first();
                // Helper::sendSMS2(1407167713545096968, $secondary_wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $SecondaryWbiMasterPayment->payment_type,
                    $secondary_wbi_master->user_id,
                    $SecondaryWbiMasterPayment->secondary_wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $SecondaryWbiMasterPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($secondary_wbi_master->user_id);

                if ($SecondaryWbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/secondary-wbi-license/' . $SecondaryWbiMasterPayment->secondary_wbi_master_id . '/edit');
                } else {
                    return redirect('/secondary-wbi-license/update/' . $SecondaryWbiMasterPayment->secondary_wbi_master_id);
                }

            default:
                return redirect('/secondary-wbi-license');
        }
    }

    public function compositeWbiafterpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            if (isset($requestdata['error'])) {
                $paymenetstatus = "FAILED";
                if (isset($requestdata['error']['metadata'])) {
                    $metadata = $requestdata['error']['metadata'];
                    if (is_string($metadata)) {
                        $decodedMetadata = json_decode($metadata, true);
                        $transactionid = $decodedMetadata['order_id'] ?? null;
                    } else if (is_array($metadata)) {
                        $transactionid = $metadata['order_id'] ?? null;
                    } else {
                        $transactionid = null;
                    }
                } else {
                    $transactionid = null;
                }
            } else {
                if (isset($requestdata['razorpay_order_id']) && isset($requestdata['razorpay_payment_id']) && isset($requestdata['razorpay_signature'])) {
                    $generated_signature = hash_hmac(
                        'sha256',
                        $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                        env('RAZ_HDFC_PAY_SECRET_KEY')
                    );
                    if ($generated_signature == $requestdata['razorpay_signature']) {
                        $paymenetstatus = "SUCCESS";
                    } else {
                        $paymenetstatus = "FAILED";
                    }

                    $transactionid = $requestdata['razorpay_order_id'];
                } else {
                    $paymenetstatus = "FAILED";
                    $transactionid = null;
                }
            }
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $CompositeWbiMasterPayment = WbiCompositePayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $CompositeWbiMasterPayment->transaction_id;
                    $amount = $CompositeWbiMasterPayment->fee;
                } else {
                    $CompositeWbiMasterPayment = WbiCompositePayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $CompositeWbiMasterPayment->status = 1; //Success
                $CompositeWbiMasterPayment->save();

                $wbi_composite = WbiComposite::where('id', $CompositeWbiMasterPayment->wbi_composite_id)->first();
                $wbi_composite->application_status = $CompositeWbiMasterPayment->payment_type == "APPLICATION" ? 23 : 24;
                $wbi_composite->save();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $CompositeWbiMasterPayment->payment_type,
                    $wbi_composite->user_id,
                    $CompositeWbiMasterPayment->wbi_composite_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $CompositeWbiMasterPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($wbi_composite->user_id);

                if ($CompositeWbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/wbi-composite/' . $CompositeWbiMasterPayment->wbi_composite_id . '/edit');
                } else {
                    return redirect('/wbi-composite/update/' . $CompositeWbiMasterPayment->wbi_composite_id);
                }
            case "FAILED":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    if(!$transactionid){
                        return redirect(env('APP_URL'))->with('error', 'Payment record not found');
                    }
                    $CompositeWbiMasterPayment = WbiCompositePayment::where('razorpay_order_id', $transactionid)->first();
                    $transactionid = $CompositeWbiMasterPayment->transaction_id;
                    $amount = $CompositeWbiMasterPayment->fee;
                } else {
                    $CompositeWbiMasterPayment = WbiCompositePayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $CompositeWbiMasterPayment->status = 0; //Failed
                $CompositeWbiMasterPayment->save();

                $wbi_composite = WbiComposite::where('id', $CompositeWbiMasterPayment->wbi_composite_id)->first();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $CompositeWbiMasterPayment->payment_type,
                    $wbi_composite->user_id,
                    $CompositeWbiMasterPayment->wbi_composite_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $CompositeWbiMasterPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($wbi_composite->user_id);

                if ($CompositeWbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/wbi-composite/' . $CompositeWbiMasterPayment->wbi_composite_id . '/edit');
                } else {
                    return redirect('/wbi-composite/update/' . $CompositeWbiMasterPayment->wbi_composite_id);
                }

            default:
                return redirect('/wbi-composite');
        }
    }

    public function dprafterpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            if (isset($requestdata['error'])) {
                $paymenetstatus = "FAILED";
                if (isset($requestdata['error']['metadata'])) {
                    $metadata = $requestdata['error']['metadata'];
                    if (is_string($metadata)) {
                        $decodedMetadata = json_decode($metadata, true);
                        $transactionid = $decodedMetadata['order_id'] ?? null;
                    } else if (is_array($metadata)) {
                        $transactionid = $metadata['order_id'] ?? null;
                    } else {
                        $transactionid = null;
                    }
                } else {
                    $transactionid = null;
                }
            } else {
                if (isset($requestdata['razorpay_order_id']) && isset($requestdata['razorpay_payment_id']) && isset($requestdata['razorpay_signature'])) {
                    $generated_signature = hash_hmac(
                        'sha256',
                        $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                        env('RAZ_HDFC_PAY_SECRET_KEY')
                    );
                    if ($generated_signature == $requestdata['razorpay_signature']) {
                        $paymenetstatus = "SUCCESS";
                    } else {
                        $paymenetstatus = "FAILED";
                    }

                    $transactionid = $requestdata['razorpay_order_id'];
                } else {
                    $paymenetstatus = "FAILED";
                    $transactionid = null;
                }
            }
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $DprPayment = DprPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $DprPayment->transaction_id;
                    $amount = $DprPayment->fee;
                } else {
                    $DprPayment = DprPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $DprPayment->status = 1; //Success
                $DprPayment->save();

                $dpr_report = DprReport::where('id', $DprPayment->dpr_report_id)->first();
                $dpr_report->doc_status = $DprPayment->payment_type == "APPLICATION" ? 23 : 24;
                $dpr_report->save();
                // Helper::sendSMS2(1407167713545096968, $secondary_wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $DprPayment->payment_type,
                    $dpr_report->user_id,
                    $DprPayment->dpr_report_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $DprPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($dpr_report->user_id);

                if ($DprPayment->payment_type == "APPLICATION") {
                    return redirect('/dpr-report/' . $DprPayment->dpr_report_id . '/edit');
                } else {
                    return redirect('/dpr-report/edit/' . $DprPayment->dpr_report_id);
                }
            case "FAILED":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    if(!$transactionid){
                        return redirect(env('APP_URL'))->with('error', 'Payment record not found');
                    }
                    $DprPayment = DprPayment::where('razorpay_order_id', $transactionid)->first();
                    $amount = $DprPayment->fee;
                    $transactionid = $DprPayment->transaction_id;
                } else {
                    $DprPayment = DprPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $DprPayment->status = 0; //Failed
                $DprPayment->save();

                $dpr_report = DprReport::where('id', $DprPayment->dpr_report_id)->first();
                // Helper::sendSMS2(1407167713545096968, $secondary_wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $DprPayment->payment_type,
                    $dpr_report->user_id,
                    $DprPayment->dpr_report_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $DprPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($dpr_report->user_id);

                if ($DprPayment->payment_type == "APPLICATION") {
                    return redirect('/dpr-report/' . $DprPayment->dpr_report_id . '/edit');
                } else {
                    return redirect('/dpr-report/update/' . $DprPayment->dpr_report_id);
                }

            default:
                return redirect('/dpr-report');
        }
    }

    public function primaryWbiafterpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            if (isset($requestdata['error'])) {
                $paymenetstatus = "FAILED";
                if (isset($requestdata['error']['metadata'])) {
                    $metadata = $requestdata['error']['metadata'];
                    if (is_string($metadata)) {
                        $decodedMetadata = json_decode($metadata, true);
                        $transactionid = $decodedMetadata['order_id'] ?? null;
                    } else if (is_array($metadata)) {
                        $transactionid = $metadata['order_id'] ?? null;
                    } else {
                        $transactionid = null;
                    }
                } else {
                    $transactionid = null;
                }
            } else {
                if (isset($requestdata['razorpay_order_id']) && isset($requestdata['razorpay_payment_id']) && isset($requestdata['razorpay_signature'])) {
                    $generated_signature = hash_hmac(
                        'sha256',
                        $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                        env('RAZ_HDFC_PAY_SECRET_KEY')
                    );

                    if ($generated_signature == $requestdata['razorpay_signature']) {
                        $paymenetstatus = "SUCCESS";
                    } else {
                        $paymenetstatus = "FAILED";
                    }

                    $transactionid = $requestdata['razorpay_order_id'];
                } else {
                    $paymenetstatus = "FAILED";
                    $transactionid = null;
                }
            }
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $WbiMasterPayment = WbiMasterPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $WbiMasterPayment->transaction_id;
                    $amount = $WbiMasterPayment->fee;
                } else {
                    $WbiMasterPayment = WbiMasterPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $WbiMasterPayment->status = 1; //Success
                $WbiMasterPayment->save();

                $wbi_master = WbiMaster::where('id', $WbiMasterPayment->wbi_master_id)->first();
                $wbi_master->application_status = $WbiMasterPayment->payment_type == "APPLICATION" ? 23 : 24;
                $wbi_master->save();

                // Helper::sendSMS2(1407167713545096968, $wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $WbiMasterPayment->payment_type,
                    $wbi_master->user_id,
                    $WbiMasterPayment->wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $WbiMasterPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($wbi_master->user_id);
                if ($WbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/wbi-license/' . $WbiMasterPayment->wbi_master_id . '/edit');
                } else {
                    return redirect('/wbi-license/update/' . $WbiMasterPayment->wbi_master_id);
                }
            case "FAILED":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    if(!$transactionid){
                        return redirect(env('APP_URL'))->with('error', 'Payment record not found');
                    }
                    $WbiMasterPayment = WbiMasterPayment::where('razorpay_order_id', $transactionid)->first();
                    $transactionid = $WbiMasterPayment->transaction_id;
                    $amount = $WbiMasterPayment->fee;
                    
                } else {
                    $WbiMasterPayment = WbiMasterPayment::where('transaction_id', $transactionid)->first();
                }
                $WbiMasterPayment->status = 0; //Failed
                $WbiMasterPayment->save();

                $wbi_master = WbiMaster::where('id', $WbiMasterPayment->wbi_master_id)->first();
                // Helper::sendSMS2(1407167713545096968, $wbi_master->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $WbiMasterPayment->payment_type,
                    $wbi_master->user_id,
                    $WbiMasterPayment->wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $WbiMasterPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($wbi_master->user_id);
                if ($WbiMasterPayment->payment_type == "APPLICATION") {
                    return redirect('/wbi-license/' . $WbiMasterPayment->wbi_master_id . '/edit');
                } else {
                    return redirect('/wbi-license/update/' . $WbiMasterPayment->wbi_master_id);
                }
            default:
                return redirect('/wbi-license');
        }
    }

    public function secondaryagarwoodWbiafterpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            if (isset($requestdata['error'])) {
                $paymenetstatus = "FAILED";
                if (isset($requestdata['error']['metadata'])) {
                    $metadata = $requestdata['error']['metadata'];
                    if (is_string($metadata)) {
                        $decodedMetadata = json_decode($metadata, true);
                        $transactionid = $decodedMetadata['order_id'] ?? null;
                    } else if (is_array($metadata)) {
                        $transactionid = $metadata['order_id'] ?? null;
                    } else {
                        $transactionid = null;
                    }
                } else {
                    $transactionid = null;
                }
            } else {
                if (isset($requestdata['razorpay_order_id']) && isset($requestdata['razorpay_payment_id']) && isset($requestdata['razorpay_signature'])) {
                    $generated_signature = hash_hmac(
                        'sha256',
                        $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                        env('RAZ_HDFC_PAY_SECRET_KEY')
                    );

                    if ($generated_signature == $requestdata['razorpay_signature']) {
                        $paymenetstatus = "SUCCESS";
                    } else {
                        $paymenetstatus = "FAILED";
                    }

                    $transactionid = $requestdata['razorpay_order_id'];
                } else {
                    $paymenetstatus = "FAILED";
                    $transactionid = null;
                }
            }
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $SecondaryAgarwoodWbiPayment = SecondaryAgarwoodWbiPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $SecondaryAgarwoodWbiPayment->transaction_id;
                    $amount = $SecondaryAgarwoodWbiPayment->fee;
                } else {
                    $SecondaryAgarwoodWbiPayment = SecondaryAgarwoodWbiPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $SecondaryAgarwoodWbiPayment->status = 1; //Success
                $SecondaryAgarwoodWbiPayment->save();

                $SecondaryAgarwoodWbiMaster = SecondaryAgarwoodWbiMaster::where('id', $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id)->first();
                $SecondaryAgarwoodWbiMaster->application_status = $SecondaryAgarwoodWbiPayment->payment_type == "APPLICATION" ? 23 : 24;
                $SecondaryAgarwoodWbiMaster->save();
                // Helper::sendSMS2(1407167713545096968, $SecondaryAgarwoodWbiMaster->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $SecondaryAgarwoodWbiPayment->payment_type,
                    $SecondaryAgarwoodWbiMaster->user_id,
                    $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $SecondaryAgarwoodWbiPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($SecondaryAgarwoodWbiMaster->user_id);
                if ($SecondaryAgarwoodWbiPayment->payment_type == "APPLICATION") {
                    return redirect('/secondary-agarwood/' . $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id . '/edit');
                } else {
                    return redirect('/secondary-agarwood/update/' . $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id);
                }
            case "FAILED":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    if(!$transactionid){
                        return redirect(env('APP_URL'))->with('error', 'Payment record not found');
                    }
                    $SecondaryAgarwoodWbiPayment = SecondaryAgarwoodWbiPayment::where('razorpay_order_id', $transactionid)->first();
                    $amount = $SecondaryAgarwoodWbiPayment->fee;
                    $transactionid = $SecondaryAgarwoodWbiPayment->transaction_id;
                } else {
                    $SecondaryAgarwoodWbiPayment = SecondaryAgarwoodWbiPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                
                $SecondaryAgarwoodWbiPayment->status = 0; //Failed
                $SecondaryAgarwoodWbiPayment->save();

                $SecondaryAgarwoodWbiMaster = SecondaryAgarwoodWbiMaster::where('id', $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id)->first();
                // Helper::sendSMS2(1407167713545096968, $SecondaryAgarwoodWbiMaster->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $SecondaryAgarwoodWbiPayment->payment_type,
                    $SecondaryAgarwoodWbiMaster->user_id,
                    $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount,
                    $SecondaryAgarwoodWbiPayment->razorpay_order_id ?? null
                );

                Auth::loginUsingId($SecondaryAgarwoodWbiMaster->user_id);
                
                if ($SecondaryAgarwoodWbiPayment->payment_type == "APPLICATION") {
                    return redirect('/secondary-agarwood/' . $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id . '/edit');
                } else {
                    return redirect('/secondary-agarwood/update/' . $SecondaryAgarwoodWbiPayment->secondary_agarwood_wbi_master_id);
                }
            default:
                return redirect('/secondary-agarwood');
        }
    }

    protected function trackpgtransaction(
        $request,
        $transaction_type,
        $user_id,
        $form_id,
        $transactionid,
        $transactionstatus,
        $amount,
        $razorpay_order_id = null
    ) {
        PaymentTransaction::create([
            'transaction_id' => $transactionid,
            'razorpay_order_id' => $razorpay_order_id,
            'transaction_status' => $transactionstatus == "SUCCESS" ? 1 : 2,
            'transaction_amount' => $amount,
            'user_id' => $user_id,
            'transaction_type' => $transaction_type,
            'form_id' => $form_id,
            'response' => $request,
        ]);
    }

    protected function getresponsedata($request)
    {
        $authKey = env('PG_AUTHENTICATION_KEY');
        $authIV = env('PC_AUTHERNTICATION_IV');

        $decText = null;
        $AesCipher = new PGAuthentication();
        $decText = $AesCipher->decrypt($authKey, $authIV, $request->encResponse);

        $token = strtok($decText, "&");
        $i = 0;
        $data = [];

        while ($token !== false) {
            $i = $i + 1;
            $token1 = strchr($token, "=");
            $token = strtok("&");
            $fstr = ltrim($token1, "=");
            if ($i == 2) {
                $data['payerEmail'] = $fstr;
            }

            if ($i == 3) {
                $data['payerMobile'] = $fstr;
            }

            if ($i == 4) {
                $data['clientTxnId'] = $fstr;
            }

            if ($i == 5) {
                $data['payerAddress'] = $fstr;
            }

            if ($i == 6) {
                $data['amount'] = $fstr;
            }

            if ($i == 7) {
                $data['clientCode'] = $fstr;
            }

            if ($i == 8) {
                $data['paidAmount'] = $fstr;
            }

            if ($i == 9) {
                $data['paymentMode'] = $fstr;
            }

            if ($i == 10) {
                $data['bankName'] = $fstr;
            }

            if ($i == 11) {
                $data['amountType'] = $fstr;
            }

            if ($i == 12) {
                $data['status'] = $fstr;
            }

            if ($i == 13) {
                $data['statusCode'] = $fstr;
            }

            if ($i == 14) {
                $data['challanNumber'] = $fstr;
            }

            if ($i == 15) {
                $data['sabpaisaTxnId'] = $fstr;
            }

            if ($i == 16) {
                $data['sabpaisaMessage'] = $fstr;
            }

            if ($i == 17) {
                $data['bankMessage'] = $fstr;
            }

            if ($i == 18) {
                $data['bankErrorCode'] = $fstr;
            }

            if ($i == 19) {
                $data['sabpaisaErrorCode'] = $fstr;
            }

            if ($i == 20) {
                $data['bankTxnId'] = $fstr;
            }

            if ($i == 21) {
                $data['transDate'] = $fstr;
            }

            if ($token == true) {

                $data['token'] = $fstr;
            }
        }

        return $data;
    }

    // Transfer of licence Composiete payment
    public function transferlicenceaftercompositepayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            $generated_signature = hash_hmac(
                'sha256',
                $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                env('RAZ_HDFC_PAY_SECRET_KEY')
            );


            if ($generated_signature == $requestdata['razorpay_signature']) {
                $paymenetstatus = "SUCCESS";
            } else {
                $paymenetstatus = "FAILED";
            }

            $transactionid = $requestdata['razorpay_order_id'];
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $transferOfLicenceCompositePayment = TransferOfLicenceCompositePayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $transferOfLicenceCompositePayment->transaction_id;
                    $amount = $transferOfLicenceCompositePayment->fee;
                } else {
                    $transferOfLicenceCompositePayment = TransferOfLicenceCompositePayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $transferOfLicenceCompositePayment->status = 1; //Success
                $transferOfLicenceCompositePayment->save();

                $transferOfLicenceComposite = TransferOfLicenceComposite::where('id', $transferOfLicenceCompositePayment->transfer_licence_id)->first();
                $transferOfLicenceComposite->application_status = 23;
                $transferOfLicenceComposite->save();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceCompositePayment->payment_type,
                    $transferOfLicenceComposite->user_id,
                    $transferOfLicenceCompositePayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );

                Auth::loginUsingId($transferOfLicenceComposite->user_id);

                if ($transferOfLicenceCompositePayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-composite/' . $transferOfLicenceCompositePayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-composite/' . $transferOfLicenceCompositePayment->transfer_licence_id);
                }
            case "FAILED":
                $transferOfLicenceCompositePayment = TransferOfLicenceCompositePayment::where('transaction_id', $transactionid)
                    ->first();
                $transferOfLicenceCompositePayment->status = 0; //Failed
                $transferOfLicenceCompositePayment->save();

                $transferOfLicenceCompositePayment = TransferOfLicenceComposite::where('id', $transferOfLicenceCompositePayment->transferOfLicence)->first();
                // Helper::sendSMS2(1407167713545096968, $transferOfLicenceCompositePayment->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceCompositePayment->payment_type,
                    $transferOfLicenceCompositePayment->user_id,
                    $transferOfLicenceCompositePayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );


                Auth::loginUsingId($transferOfLicenceCompositePayment->user_id);

                if ($transferOfLicenceCompositePayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-composite/' . $transferOfLicenceCompositePayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-composite/' . $transferOfLicenceCompositePayment->transfer_licence_id);
                }

            default:
                return redirect('/user/transfer-licence-composite');
        }
    }
    // Transfer of licence primary payment 
    public function transferlicenceafterprimarypayment(Request $request)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($request->STATUS) ? $request->STATUS : "";
            //$request = $request;
            $transactionid = isset($request->ORDER_ID) ? $request->ORDER_ID : "";
            $amount = isset($request->AMOUNT) ? $request->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($request?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            //$request = $request;
            $generated_signature = hash_hmac(
                'sha256',
                $request['razorpay_order_id'] . '|' . $request['razorpay_payment_id'],
                env('RAZ_HDFC_PAY_SECRET_KEY')
            );


            if ($generated_signature == $request['razorpay_signature']) {
                $paymenetstatus = "SUCCESS";
            } else {
                $paymenetstatus = "FAILED";
            }

            $transactionid = $request['razorpay_order_id'];
        }

        switch ($paymenetstatus) {
            case "SUCCESS":

                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $transferOfLicencePrimaryPayment = TransferOfLicencePrimaryPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $transferOfLicencePrimaryPayment->transaction_id;
                    $amount = $transferOfLicencePrimaryPayment->fee;
                } else {
                    $transferOfLicencePrimaryPayment = TransferOfLicencePrimaryPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $transferOfLicencePrimaryPayment->status = 1; //Success
                $transferOfLicencePrimaryPayment->save();

                $transferLicencePrimary = TransferLicencePrimary::where('id', $transferOfLicencePrimaryPayment->transfer_licence_id)->first();
                log::info(["transfer" => $transferLicencePrimary]);
                $transferLicencePrimary->application_status = 23;
                $transferLicencePrimary->save();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);

                $this->trackpgtransaction(
                    $request,
                    $transferOfLicencePrimaryPayment->payment_type,
                    $transferLicencePrimary->user_id,
                    $transferOfLicencePrimaryPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );

                Auth::loginUsingId($transferLicencePrimary->user_id);

                if ($transferOfLicencePrimaryPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-primary/' . $transferOfLicencePrimaryPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-primary/' . $transferOfLicencePrimaryPayment->transfer_licence_id);
                }
            case "FAILED":
                $transferOfLicencePrimaryPayment = TransferOfLicencePrimaryPayment::where('transaction_id', $transactionid)
                    ->first();
                $transferOfLicencePrimaryPayment->status = 0; //Failed
                $transferOfLicencePrimaryPayment->save();

                $transferLicencePrimary = TransferLicencePrimary::where('id', $transferOfLicencePrimaryPayment->transfer_licence_id)->first();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicencePrimaryPayment->payment_type,
                    $transferLicencePrimary->user_id,
                    $transferOfLicencePrimaryPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );

                Auth::loginUsingId($transferLicencePrimary->user_id);

                if ($transferOfLicencePrimaryPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-primary/' . $transferOfLicencePrimaryPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-primary/' . $transferOfLicencePrimaryPayment->transfer_licence_id);
                }

            default:
                return redirect('user/transfer-licence-primary');
        }
    }

    // Transfer of licence Secondary wbi non agarwood  payment 
    public function transferlicenceaftersecondarywbinonagarwoodpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            $generated_signature = hash_hmac(
                'sha256',
                $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                env('RAZ_HDFC_PAY_SECRET_KEY')
            );


            if ($generated_signature == $requestdata['razorpay_signature']) {
                $paymenetstatus = "SUCCESS";
            } else {
                $paymenetstatus = "FAILED";
            }

            $transactionid = $requestdata['razorpay_order_id'];
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $transferOfLicenceSecondayNonAgarwoodPayment = TransferOfLicenceSecondayNonAgarwoodPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $transferOfLicenceSecondayNonAgarwoodPayment->transaction_id;
                    $amount = $transferOfLicenceSecondayNonAgarwoodPayment->fee;
                } else {
                    $transferOfLicenceSecondayNonAgarwoodPayment = TransferOfLicenceSecondayNonAgarwoodPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $transferOfLicenceSecondayNonAgarwoodPayment->status = 1; //Success
                $transferOfLicenceSecondayNonAgarwoodPayment->save();

                $transferOfLicenceSecondaryNonAgrawood = TransferOfLicenceSecondaryNonAgrawood::where('id', $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id)->first();
                $transferOfLicenceSecondaryNonAgrawood->application_status = 23;
                $transferOfLicenceSecondaryNonAgrawood->save();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceSecondayNonAgarwoodPayment->payment_type,
                    $transferOfLicenceSecondaryNonAgrawood->user_id,
                    $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );


                Auth::loginUsingId($transferOfLicenceSecondaryNonAgrawood->user_id);

                if ($transferOfLicenceSecondayNonAgarwoodPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-secondary-wbi-non-agarwood/' . $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-secondary-wbi-non-agarwood/' . $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id);
                }
            case "FAILED":
                $transferOfLicenceSecondayNonAgarwoodPayment = TransferOfLicenceSecondayNonAgarwoodPayment::where('transaction_id', $transactionid)
                    ->first();
                $transferOfLicenceSecondayNonAgarwoodPayment->status = 0; //Failed
                $transferOfLicenceSecondayNonAgarwoodPayment->save();

                $transferOfLicenceSecondaryNonAgrawood = TransferOfLicenceSecondaryNonAgrawood::where('id', $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id)->first();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceSecondayNonAgarwoodPayment->payment_type,
                    $transferOfLicenceSecondaryNonAgrawood->user_id,
                    $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );


                Auth::loginUsingId($transferOfLicenceSecondaryNonAgrawood->user_id);

                if ($transferOfLicenceSecondayNonAgarwoodPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-secondary-wbi-non-agarwood/' . $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-secondary-wbi-non-agarwood/' . $transferOfLicenceSecondayNonAgarwoodPayment->transfer_licence_id);
                }

            default:
                return redirect('user/transfer-licence-secondary-wbi-non-agarwood');
        }
    }
    // Transfer of licence Secondary wbi non agarwood  payment 
    public function transferlicenceaftersecondarywbiagarwoodpayment(Request $requestdata)
    {
        if (env('PAYMENT_METHOD') == "tokapoisa") {
            $paymenetstatus = isset($requestdata->STATUS) ? $requestdata->STATUS : "";
            $request = $requestdata;
            $transactionid = isset($requestdata->ORDER_ID) ? $requestdata->ORDER_ID : "";
            $amount = isset($requestdata->AMOUNT) ? $requestdata->AMOUNT : "";
        } else if (env('PAYMENT_METHOD') == "sabpaisa") {
            $request = PaymentHelper::getresponsedata($requestdata?->encResponse);
            $paymenetstatus = isset($request['status']) ? $request['status'] : "";
            $transactionid = isset($request['clientTxnId']) ? $request['clientTxnId'] : "";
            $amount = isset($request['amount']) ? $request['amount'] : "";
        } else if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
            $request = $requestdata;
            $generated_signature = hash_hmac(
                'sha256',
                $requestdata['razorpay_order_id'] . '|' . $requestdata['razorpay_payment_id'],
                env('RAZ_HDFC_PAY_SECRET_KEY')
            );
            if ($generated_signature == $requestdata['razorpay_signature']) {
                $paymenetstatus = "SUCCESS";
            } else {
                $paymenetstatus = "FAILED";
            }

            $transactionid = $requestdata['razorpay_order_id'];
        }

        switch ($paymenetstatus) {
            case "SUCCESS":
                if (env('PAYMENT_METHOD') == "razorpay_hdfc") {
                    $transferOfLicenceSecondayAgarwoodPayment = TransferOfLicenceSecondayAgarwoodPayment::where('razorpay_order_id', $transactionid)
                        ->first();
                    $transactionid = $transferOfLicenceSecondayAgarwoodPayment->transaction_id;
                    $amount = $transferOfLicenceSecondayAgarwoodPayment->fee;
                } else {
                    $transferOfLicenceSecondayAgarwoodPayment = TransferOfLicenceSecondayAgarwoodPayment::where('transaction_id', $transactionid)
                        ->first();
                }
                $transferOfLicenceSecondayAgarwoodPayment->status = 1; //Success
                $transferOfLicenceSecondayAgarwoodPayment->save();

                $transferOfLicenceSecondaryAgrawood = TransferOfLicenceSecondaryAgrawood::where('id', $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id)->first();
                $transferOfLicenceSecondaryAgrawood->application_status = 23;
                $transferOfLicenceSecondaryAgrawood->save();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceSecondayAgarwoodPayment->payment_type,
                    $transferOfLicenceSecondaryAgrawood->user_id,
                    $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );

                Auth::loginUsingId($transferOfLicenceSecondaryAgrawood->user_id);

                if ($transferOfLicenceSecondayAgarwoodPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-secondary-wbi-agarwood/' . $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-secondary-wbi-agarwood/' . $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id);
                }
            case "FAILED":
                $transferOfLicenceSecondayAgarwoodPayment = TransferOfLicenceSecondayAgarwoodPayment::where('transaction_id', $transactionid)
                    ->first();
                $transferOfLicenceSecondayAgarwoodPayment->status = 0; //Failed
                $transferOfLicenceSecondayAgarwoodPayment->save();

                $transferOfLicenceSecondaryAgrawood = TransferOfLicenceSecondaryAgrawood::where('id', $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id)->first();
                // Helper::sendSMS2(1407167713545096968, $wbi_composite->user->mobile, []);
                $this->trackpgtransaction(
                    $request,
                    $transferOfLicenceSecondayAgarwoodPayment->payment_type,
                    $transferOfLicenceSecondaryAgrawood->user_id,
                    $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id,
                    $transactionid,
                    $paymenetstatus,
                    $amount
                );

                Auth::loginUsingId($transferOfLicenceSecondaryAgrawood->user_id);

                if ($transferOfLicenceSecondayAgarwoodPayment->payment_type == "TRANSFER") {
                    return redirect('user/edit-transfer-licence-secondary-wbi-agarwood/' . $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id);
                } else {
                    return redirect('user/edit-transfer-licence-secondary-wbi-agarwood/' . $transferOfLicenceSecondayAgarwoodPayment->transfer_licence_id);
                }

            default:
                return redirect('user/transfer-licence-secondary-wbi-agarwood');
        }
    }
}
